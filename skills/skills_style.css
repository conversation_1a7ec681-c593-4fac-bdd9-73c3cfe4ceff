nav{
    background: #0082e6;
    
    width: 100%;
    height: 80px;
}
label.logo{
    color: white;
    font-size: 35px;
    line-height: 80px;
    font-weight: bold;
}
nav ul{
    float: right;
    margin-right: 20px;
}

nav ul li{
    display: inline-block;
    line-height: 80px;
    margin: 0 5px;
}

nav ul li a{
    color: white;
    font-size: 17px;
    padding: 7px 13px;
    border-radius: 3px;
    text-transform: uppercase;
}

a.active,a:hover{
    background: #1b9bff;
    transition: .5s;
}

.checkbtn{
    font-size: 30px;
    color: white;
    float: right;
    line-height: 80px;
    margin-right: 40px;
    cursor: pointer;
    display: none;
}

#check{
    display: none;
}

@media (max-width: 952px){
    label.logo{
        font-size: 30px;
        padding-left: 50px;
    }

    nav ul li a{
        font-size: 16px;
    }
}

@media (max-width: 858px){
    .checkbtn{
        display: block;
    }
    ul{
        position: fixed;
        width: 100%;
        height:100vh;
        background: #2c3e50;
        top: 80px;
        left: -100%;
        text-align: center;
        transition: all .5s;
    }
    nav ul li{
        display: block;
        margin: 50px 0;
        line-height: 30px;
    }
    nav ul li a{
        font-size: 20px;
    }
    a:hover,a.active{
        background: none;
        color: #0082e6;
    }
    #check:checked ~ ul{
        left: 0;
    }
}

.inner{
    padding-top: 20px;
}

.header{
    text-align: center;
    color: #0082e6;
    padding: 1rem;
    position: relative;
}

.header::after{
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    height: 4px;
    width: 100px;
    background-color: whitesmoke;
}

.container{
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    justify-content: center;
    align-items: center;
    text-align: center;
    grid-gap: 1rem;
    padding: 1rem 80px;
    font-size: 1.2rem;
}

.skill-box{
    padding: 1rem;
    color: #0082e6;
    cursor: pointer;
}

.skill-title{
    display: flex;
    flex-direction: column;
    align-items: center;
    padding:  0.5rem;
    margin-bottom: 0.5rem;
}

.img{
    width: 90px;
    height: 90px;
    position: relative;
    border-radius: 45px;
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
}

.skill-title h3{
    color: #0082e6;
    margin: 0.5rem;
}

.skill-icon{
    width: 50px;
}

.projects{
    text-align: center;
    font-size: 40px;
    color: #2c3e50;
}