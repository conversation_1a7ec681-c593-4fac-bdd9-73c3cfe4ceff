*{
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

html,body{
    display: grid;
    height: 100%;
    background: white;
}

nav{
    background: #0082e6;
    
    width: 100%;
    height: 80px;
}
label.logo{
    color: white;
    font-size: 35px;
    line-height: 80px;
    font-weight: bold;
}
nav ul{
    float: right;
    margin-right: 20px;
}

nav ul li{
    display: inline-block;
    line-height: 80px;
    margin: 0 5px;
}

nav ul li a{
    color: white;
    font-size: 17px;
    padding: 7px 13px;
    border-radius: 3px;
    text-transform: uppercase;
}

a.active,a:hover{
    background: #1b9bff;
    transition: .5s;
}

.checkbtn{
    font-size: 30px;
    color: white;
    float: right;
    line-height: 80px;
    margin-right: 40px;
    cursor: pointer;
    display: none;
}

#check{
    display: none;
}

@media (max-width: 952px){
    label.logo{
        font-size: 30px;
        padding-left: 50px;
    }

    nav ul li a{
        font-size: 16px;
    }
}

@media (max-width: 858px){
    .checkbtn{
        display: block;
    }
    ul{
        position: fixed;
        width: 100%;
        height:100vh;
        background: #2c3e50;
        top: 80px;
        left: -100%;
        text-align: center;
        transition: all .5s;
    }
    nav ul li{
        display: block;
        margin: 50px 0;
        line-height: 30px;
    }
    nav ul li a{
        font-size: 20px;
    }
    a:hover,a.active{
        background: none;
        color: #0082e6;
    }
    #check:checked ~ ul{
        left: 0;
    }
}

.con{
    width: 400px;
    background: #0082e6;
    padding: 20px 30px;
    border: 1px solid #444;
    border-radius: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
}
.con, .stars input{
    font-size: 40px;
    color: #444;
    padding: 10px;
    transition: all 0.2s ease;
}
form header{
    width: 100%;
    font-size: 25px;
    color: white;
    font-weight: 500;
    margin: 5px 0 20px 0;
    text-align: center;
    transition: all 0.2s ease;
}
form .textarea{
    height: 100px;
    width: 100%;
    overflow: hidden;
}
form .textarea textarea{
    height: 100%;
    width: 100%;
    outline: none;
    color: rgb(0, 0, 0);
    border: 1px solid #333;
    padding: 10px;
    font-size: 17px;
}
form .btn{
    height: 45px;
    width: 100%;
    margin: 15px 0;
}
form .btn button{
    height: 100%;
    width: 100%;
    font-size: 17px;
    cursor: pointer;
    text-transform: uppercase;
    background: #111;
    outline: none;
}
#rate-1:checked ~ form header::before{
    content: "Very Bad!💔";
}
#rate-2:checked ~ form header::before{
    content: "Just Bad!💘";
}
#rate-3:checked ~ form header::before{
    content: "Normal!🤎";
}
#rate-4:checked ~ form header::before{
    content: "Just Good!💓";
}
#rate-5:checked ~ form header::before{
    content: "Very Good!💖";
}