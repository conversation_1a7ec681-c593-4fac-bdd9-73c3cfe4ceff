<!DOCTYPE html>
<html>
    <head>
        <title>Portfolio</title>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
    </head>
    <body>
        <nav>
            <input type="checkbox" id="check">
            <label for="check" class="checkbtn">
                <i class="fa fa-bars"></i>
            </label>
            <label class="logo"><PERSON><PERSON>a</label>
            <ul>
                <li><a class="active" href="/index.html"><i>Home</i></a></li>
                <li><a href="/about/about.html"><i>About</i></a></li>
                <li><a href="/skills/skills.html"><i>Skills</i></a></li>
                <li><a href="/contact/contact.html"><i>Contact</i></a></li>
                <li><a href="/review/review.html"><i>REVIEW</i></a></li>
                
            </ul>
            
        </nav>
        <h1><i>Thank you for rating my site!</i></h1>
        <style>
            body{
                background-image: linear-gradient(rgba(0, 0, 85, 0.8),rgba(0, 0, 85, 0.8)),url(flower.gif);
                background-position: center;
                background-size: cover; 
            }
            h1{
                text-align: center;
                padding: 300px;
                font-family: 'Lucida Sans Unicode', sans-serif;
                color: white;
                font-size: large;
            }
            nav{
                    background: #0082e6;
                    
                    width: 100%;
                    height: 80px;
                }
                label.logo{
                    color: white;
                    font-size: 35px;
                    line-height: 80px;
                    font-weight: bold;
                }
                nav ul{
                    float: right;
                    margin-right: 20px;
                }

                nav ul li{
                    display: inline-block;
                    line-height: 80px;
                    margin: 0 5px;
                }

                nav ul li a{
                    color: white;
                    font-size: 17px;
                    padding: 7px 13px;
                    border-radius: 3px;
                    text-transform: uppercase;
                }

                a.active,a:hover{
                    background: #1b9bff;
                    transition: .5s;
                }

                .checkbtn{
                    font-size: 30px;
                    color: white;
                    float: right;
                    line-height: 80px;
                    margin-right: 40px;
                    cursor: pointer;
                    display: none;
                }

                #check{
                    display: none;
                }

                @media (max-width: 952px){
                    label.logo{
                        font-size: 30px;
                        padding-left: 50px;
                    }

                    nav ul li a{
                        font-size: 16px;
                    }
                }

                @media (max-width: 858px){
                    .checkbtn{
                        display: block;
                    }
                    ul{
                        position: fixed;
                        width: 100%;
                        height:100vh;
                        background: #2c3e50;
                        top: 80px;
                        left: -100%;
                        text-align: center;
                        transition: all .5s;
                    }
                    nav ul li{
                        display: block;
                        margin: 50px 0;
                        line-height: 30px;
                    }
                    nav ul li a{
                        font-size: 20px;
                    }
                    a:hover,a.active{
                        background: none;
                        color: #0082e6;
                    }
                    #check:checked ~ ul{
                        left: 0;
                    }
                }
        </style>
    </body>
</html>
