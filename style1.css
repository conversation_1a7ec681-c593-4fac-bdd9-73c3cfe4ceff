*{
    padding: 0;
    margin: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
}
body{
    font-family:'Times New Roman', Times, serif;
    background-color: white;
}
nav{
    background: #0082e6;
    
    width: 100%;
    height: 80px;
}
label.logo{
    color: white;
    font-size: 35px;
    line-height: 80px;
    font-weight: bold;
}
nav ul{
    float: right;
    margin-right: 20px;
}

nav ul li{
    display: inline-block;
    line-height: 80px;
    margin: 0 5px;
}

nav ul li a{
    color: white;
    font-size: 17px;
    padding: 7px 13px;
    border-radius: 3px;
    text-transform: uppercase;
}

a.active,a:hover{
    background: #1b9bff;
    transition: .5s;
}

.checkbtn{
    font-size: 30px;
    color: white;
    float: right;
    line-height: 80px;
    margin-right: 40px;
    cursor: pointer;
    display: none;
}

#check{
    display: none;
}

@media (max-width: 952px){
    label.logo{
        font-size: 30px;
        padding-left: 50px;
    }

    nav ul li a{
        font-size: 16px;
    }
}

@media (max-width: 858px){
    .checkbtn{
        display: block;
    }
    ul{
        position: fixed;
        width: 100%;
        height:100vh;
        background: #2c3e50;
        top: 80px;
        left: -100%;
        text-align: center;
        transition: all .5s;
    }
    nav ul li{
        display: block;
        margin: 50px 0;
        line-height: 30px;
    }
    nav ul li a{
        font-size: 20px;
    }
    a:hover,a.active{
        background: none;
        color: #0082e6;
    }
    #check:checked ~ ul{
        left: 0;
    }
}

.home{
    width: 100%;
    hight: 600px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;
    color: black;
    /* text-align: center;
    font-weight: bold;
    font-size: large;
    padding: 250px; */
}

.home h1{
    color: rgb(0, 109, 233);
    font-size: 70px;
    margin-top: 80px;
}

.home h2{
    color: rgb(4, 129, 134);
    font-size: 40px;

}

.home p{
    color: #2c3e50;
    margin: 20px auto;
    font-weight: 100;
    line-height: 25px;
    font-size: 20px;
}

.home a{
    text-decoration: none;
    font-size: 1.7rem;
    font-family: sana-serif;
    color: white;
    background: #0082e6;
    width: 340px;
    padding: 20px 0px;
    text-align: center;
    border-radius: 10px;
}

.home a span{
    position: relative;
    width: 1.5rem;
    /* visibility: hidden; */
}

.home a span:after{
    content: '\21D3';
    font-size: 1.5rem;
    position: absolute;
    padding-left: 10px;
    animation: down 1s linear infinite;
}

@keyframes down {
    from {
        top: -10px;
        opacity: 0;
    }
    to{
        top: 10px;
        opacity: 1;
    }
}
