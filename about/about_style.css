*{
    padding: 0;
    margin: 0;
    text-decoration: none;
    list-style: none;
    box-sizing: border-box;
}
body{
    font-family:'Times New Roman', Times, serif;
    background-color: white;
}
nav{
    background: #0082e6;
    
    width: 100%;
    height: 80px;
}
label.logo{
    color: white;
    font-size: 35px;
    line-height: 80px;
    font-weight: bold;
}
nav ul{
    float: right;
    margin-right: 20px;
}

nav ul li{
    display: inline-block;
    line-height: 80px;
    margin: 0 5px;
}

nav ul li a{
    color: white;
    font-size: 17px;
    padding: 7px 13px;
    border-radius: 3px;
    text-transform: uppercase;
}

a.active,a:hover{
    background: #1b9bff;
    transition: .5s;
}

.checkbtn{
    font-size: 30px;
    color: white;
    float: right;
    line-height: 80px;
    margin-right: 40px;
    cursor: pointer;
    display: none;
}

#check{
    display: none;
}

@media (max-width: 952px){
    label.logo{
        font-size: 30px;
        padding-left: 50px;
    }

    nav ul li a{
        font-size: 16px;
    }
}

@media (max-width: 858px){
    .checkbtn{
        display: block;
    }
    ul{
        position: fixed;
        width: 100%;
        height:100vh;
        background: #2c3e50;
        top: 80px;
        left: -100%;
        text-align: center;
        transition: all .5s;
    }
    nav ul li{
        display: block;
        margin: 50px 0;
        line-height: 30px;
    }
    nav ul li a{
        font-size: 20px;
    }
    a:hover,a.active{
        background: none;
        color: #0082e6;
    }
    #check:checked ~ ul{
        left: 0;
    }
}

.about{
    width: 100%;
    height: auto;
    background-color: white;
    display: flex;
    justify-content: center;
    align-items: center;
}

.about .content img{
    height: auto;
    width: 600px;
    max-width: 100%;
}

.text{
    width: 550px;
    max-width: 100%;
    padding: 0 10px;
}

.content{
    width: 1280px;
    max-width: 95%;
    margin: 0 auto;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: space-around;
}

.text h1{
    color: #0082e6;
    font-size: 85px;
    margin-bottom: 20px;
    text-transform: capitalize;
}

.text h5{
    color: #2c3e50;
    font-size: 25px;
    margin-bottom: 25px;
    text-transform: capitalize;
    letter-spacing: 2px;
}

.text p{
    color: #2c3e50;
    font-size: 18px;
    line-height: 28px;
    letter-spacing: 1px;
    margin-bottom: 45px;
}