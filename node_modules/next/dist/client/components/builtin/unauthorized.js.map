{"version": 3, "sources": ["../../../../src/client/components/builtin/unauthorized.tsx"], "sourcesContent": ["import { HTTPAccessErrorFallback } from '../http-access-fallback/error-fallback'\n\nexport default function Unauthorized() {\n  return (\n    <HTTPAccessErrorFallback\n      status={401}\n      message=\"You're not authorized to access this page.\"\n    />\n  )\n}\n"], "names": ["Unauthorized", "HTTPAccessErrorFallback", "status", "message"], "mappings": ";;;;+BAEA;;;eAAwBA;;;;+BAFgB;AAEzB,SAASA;IACtB,qBACE,qBAACC,sCAAuB;QACtBC,QAAQ;QACRC,SAAQ;;AAGd", "ignoreList": [0]}